import React from 'react';
import { View, StyleSheet, Text, FlatList } from 'react-native';
import SectionHeader from './SectionHeader';
import { Colors } from 'constants/theme/colors';
import { ThemeFonts } from 'constants/theme/fonts';
import useDeviceTimerStore from 'store/deviceTimerStore';
import useMoodStore from 'store/moodStore';
import useSleepStore from 'store/sleepStore';
import useNutritionMealRecordStore from 'store/nutritionMealRecordStore';
import useUserWeightStore from 'store/userWeightStore';
import { CustomLoader } from 'components/CustomAction';
import formatDuration from 'screens/Menu Screens/Device/utils/formartDuration';
import { useAuth } from 'context/AuthContext';
import { screenWidth } from 'constants/sizes';
import SkeletonItem from 'components/CustomSkeltonLoader/AdvancedSkeletonLoader';
import useActivityStore from 'store/activityStore';
import { movementOptions } from 'constants/options';

const SummaryCard = ({ title, value, index, isLoading, isLoadingSummary }) => {
    return (
        <View style={[styles.cardContainer, { marginLeft: index % 2 === 0 ? 0 : 48 }]}>
            <View style={[{ backgroundColor: Colors.darkGreen, borderTopLeftRadius: 25, borderTopRightRadius: 25 }]}>
                <View style={[styles.card, isLoadingSummary ? { opacity: .8 } : {}]} >
                    {
                        isLoadingSummary ? (
                            <View style={{ flexDirection: "column", gap: 2, justifyContent: "center", alignItems: "flex-start", flex: 1, aspectRatio: 1, backgroundColor: "rgba(255,255,255,0.2)", padding: 8 }}>
                                <SkeletonItem
                                    width="100%"
                                    height={12}
                                    borderRadius={25}
                                    isLoading={true}
                                />
                                <SkeletonItem
                                    width="100%"
                                    height={12}
                                    borderRadius={25}
                                    isLoading={true}
                                />
                                <SkeletonItem
                                    width="50%"
                                    height={12}
                                    borderRadius={25}
                                    isLoading={true}
                                />
                            </View>
                        ) : (
                            <Text style={styles.cardText} numberOfLines={3} ellipsizeMode='tail' allowFontScaling={false}>
                                {title}
                            </Text>
                        )
                    }
                </View>
            </View>
            <View style={styles.cardBottom}>
                <SkeletonItem width="70%" height={16} borderRadius={25} isLoading={isLoading}>
                    <Text style={styles.cardBottomText} allowFontScaling={false}>
                        {value}
                    </Text>
                </SkeletonItem>
            </View>
        </View>
    )
}

const SummarySection = ({
    title,
    onNavigate,
    style,
    headerStyle,
}) => {

    const { state: { user: { goals, deviceUsageLimit, weight } } } = useAuth();

    // Device timer data
    const isLoadingTimerHistory = useDeviceTimerStore(state => state.isLoadingTimerHistory);
    const isLoadingDeviceTimerHighlights = useDeviceTimerStore(state => state.isLoadingDeviceTimerHighlights);
    const totalUserTime = useDeviceTimerStore(state => state.totalUserTime);
    const deviceTimerHighlights = useDeviceTimerStore(state => state.deviceTimerHighlights);

    // Mood data
    const isLoadingLastLoggedMood = useMoodStore(state => state.isLoadingLastLoggedMood);
    const isLoadingMoodHighlights = useMoodStore(state => state.isLoadingMoodHighlights);
    const lastLoggedMood = useMoodStore(state => state.lastLoggedMood);
    const moodHighlights = useMoodStore(state => state.moodHighlights);

    // Sleep data
    const isLoadingLastSleepLogged = useSleepStore(state => state.isLoadingLastSleepLogged);
    const isLoadingSleepHighLights = useSleepStore(state => state.isLoadingSleepHighLights);
    const lastLoggedSleep = useSleepStore(state => state.lastLoggedSleep);
    const sleepHighlights = useSleepStore(state => state.sleepHighlights);

    // Nutrition data
    const isLoadingMealRecords = useNutritionMealRecordStore(state => state.isLoadingMealRecords);
    const isLoadingNutritionHighlights = useNutritionMealRecordStore(state => state.isLoadingNutritionHighlights);
    const todayTotalCalories = useNutritionMealRecordStore(state => state.todayTotalCalories);
    const nutritionHighlights = useNutritionMealRecordStore(state => state.nutritionHighlights);

    // Weight data
    const isLoadingLastLoggedWeightData = useUserWeightStore(state => state.isLoadingLastLoggedWeightData);
    const isLoadingWeightHighlights = useUserWeightStore(state => state.isLoadingWeightHighlights);
    const lastLoggedWeight = useUserWeightStore(state => state.lastLoggedWeight);
    const weightHighlights = useUserWeightStore(state => state.weightHighlights);

    const isLoadingActivityHighlights = useActivityStore(state => state.isLoadingActivityHighlights);
    const activityHighlights = useActivityStore(state => state.activityHighlights);
    const activityLedgerData = useActivityStore(state => state.activityLedgerData);

    return (
        <View style={[styles.container, style]}>
            <SectionHeader
                title={title}
                onNavigate={onNavigate}
                showTime={false}
                style={headerStyle}
            />

            <View style={{ flexDirection: "row", justifyContent: "space-between", gap: 24, flexWrap: "wrap" }}>
                <SummaryCard title={deviceTimerHighlights.length > 0 ? deviceTimerHighlights[0] : "No device summary"} value={`${formatDuration(totalUserTime)}` + (deviceUsageLimit ? `/${deviceUsageLimit} hr` : "")} index={0} isLoading={isLoadingTimerHistory} isLoadingSummary={isLoadingDeviceTimerHighlights} />

                <SummaryCard title={moodHighlights.length > 0 ? moodHighlights[0] : "No mood summary"} value={
                    lastLoggedMood?.moodType
                        ? lastLoggedMood?.moodType[0].toUpperCase() +
                        lastLoggedMood?.moodType.slice(1)
                        : "Enter mood"
                } index={0} isLoading={isLoadingLastLoggedMood} isLoadingSummary={isLoadingMoodHighlights} />
                {/* </View> */}
                {/* <View style={{ flexDirection: "row", justifyContent: "space-between", gap: 24, marginTop: 16 }}> */}
                <SummaryCard title={sleepHighlights.length > 0 ? sleepHighlights[0] : "No sleep summary"} value={`${lastLoggedSleep?.numOfHours || 0} hr/${goals.filter((goal) => goal?.goal_type === "sleep")[0]
                    ?.selected_goal
                    } hr` || ""
                } index={0} isLoading={isLoadingLastSleepLogged} isLoadingSummary={isLoadingSleepHighLights} />

                <SummaryCard title={nutritionHighlights.length > 0 ? nutritionHighlights[0] : "No nutrition summary"} value={`${todayTotalCalories} cal`} index={0} isLoading={isLoadingMealRecords} isLoadingSummary={isLoadingNutritionHighlights} />
                {/* </View> */}
                {/* <View style={{ flexDirection: "row", justifyContent: "space-between", gap: 24, marginTop: 16 }}> */}
                <SummaryCard title={weightHighlights.length > 0 ? weightHighlights[0] : "No weight summary"} value={`${Number(lastLoggedWeight?.weight || weight).toFixed(0)} kg`} index={0} isLoading={isLoadingLastLoggedWeightData} isLoadingSummary={isLoadingWeightHighlights} />

                <SummaryCard title={activityHighlights.length > 0 ? activityHighlights[0] : "No activity summary"} value={`${Number(activityLedgerData?.totalBurnedCalories || 0).toFixed(0)}/${movementOptions.filter((option) => option.value === goals.filter((goal) => goal?.goal_type === "movement")[0].selected_goal)[0].label.split(" ")[1].slice(0, -4)
                    } cal` || ""
                } index={0} isLoading={isLoadingActivityHighlights} isLoadingSummary={isLoadingActivityHighlights} />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        // backgroundColor: Colors.lightGreen
    },
    cardContainer: {
        // flex: 1,
        // height: 100,
        backgroundColor: Colors.white,
        borderRadius: 25,
        padding: 12,
        width: (screenWidth - 76) / 2
    },
    card: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        aspectRatio: 1,
        borderColor: Colors.black,
        borderRadius: 25,
        padding: 12,
        backgroundColor: Colors.white,
        transform: [{ translateX: -8 }, { translateY: -10 }],
        overflow: "hidden"
    },
    cardText: {
        fontSize: 11,
        fontFamily: ThemeFonts.Exo_700,
        color: Colors.black,
        textAlign: "center"
    },
    cardBottom: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: Colors.darkGreen,
        borderBottomEndRadius: 25,
        borderBottomStartRadius: 25,
        padding: 12,
        paddingTop: 1
    },
    cardBottomText: {
        fontSize: 16,
        fontFamily: ThemeFonts.Exo_700,
        color: Colors.white,
        textAlign: "center"
    }
});

export default SummarySection;
