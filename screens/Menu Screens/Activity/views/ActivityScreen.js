import {
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useEffect, useState } from "react";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { Colors } from "constants/theme/colors";
import { ThemeFonts } from "constants/theme/fonts";
import { CustomButton, CustomLoader } from "components/CustomAction";
import { useNavigation, useRoute } from "@react-navigation/native";
import VideoOverviewCard from "components/CustomCards/VideoOverviewCard";
import { getYouTubeVideoId } from "utils/youtube";
import YouTubePlayerModal from "components/CustomVideo/YouTubePlayerModal";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import useActivityStore from "store/activityStore";
import HighlightCard from "components/CustomCards/HighlightCard";
import ActivityLedgerCard from "../components/ActivityLedgerCard";
import { RefreshControl } from "react-native";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";
import FormattedTime from "components/CustomText/FormattedTime";

const videoData = [
  {
    id: 1,
    videoUrl: "https://www.youtube.com/watch?v=brjAjq4zEIE",
    title: "Yoga",
    description:
      "A relaxing yoga session to improve flexibility and mindfulness.",
  },
  {
    id: 2,
    videoUrl: "https://www.youtube.com/watch?v=C2HX2pNbUCM",
    title: "30 min Pilates",
    description:
      "A full-body Pilates workout to strengthen your core and improve posture.",
  },
  {
    id: 3,
    videoUrl: "https://www.youtube.com/watch?v=s7K-hT9gZtg&gad_source=2",
    title: "30 min Abs workout",
    description:
      "An intense abs workout to help sculpt and tone your core muscles.",
  },
  {
    id: 4,
    videoUrl: "https://www.youtube.com/watch?v=H1F-UfC8Mb8",
    title: "30 min strength training.",
    description:
      "A challenging strength training session to build muscle and endurance.",
  },
];

const ActivityScreen = () => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();

  const { isLoadingActivityHighlights, isLoadingActivityLedger, isLoadingRecentActivity, recentActivity, activityHighlights, activityHighlightsError } = useActivityStore((state) => state);
  const { getActivityHighlights, getTodaysLedger, getRecentActivity } = useActivityStore(state => state);

  const [refreshing, setRefreshing] = useState(false);

  const [showVideo, setShowVideo] = useState(false);
  const [currentVideo, setCurrentVideo] = useState({
    id: "",
    title: "",
    description: "",
  });

  const handlePlayVideo = (video) => {
    const videoId = getYouTubeVideoId(video.videoUrl);
    if (videoId) {
      setCurrentVideo({
        id: videoId,
        title: video.title,
        description: video.description,
      });
      setShowVideo(true);
    }
  };

  useEffect(() => {
    getActivityHighlights();
    getTodaysLedger();
    getRecentActivity();
  },
    []);

  if (showVideo) {
    return (
      <YouTubePlayerModal
        videoId={currentVideo.id}
        title={currentVideo.title}
        description={currentVideo.description}
        onClose={() => {
          setShowVideo(false);
          setCurrentVideo({ id: "", title: "", description: "" });
        }}
        topInset={insets.top}
      />
    );
  }

  return (
    <AppLayout illustration={true}>
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ flexGrow: 1, paddingBottom: 10 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => {
              setRefreshing(true);
              getTodaysLedger();
              getActivityHighlights();
              getRecentActivity();
              setRefreshing(false);
            }}
          />
        }
      >
        <TouchableWithoutFeedback>
          <View style={styles.container}>
            <View style={styles.header}>
              <Text style={styles.headerTitle}>Activity</Text>
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginHorizontal: 16,
                  marginTop: 8
                }}
              >
                <SkeletonItem isLoading={isLoadingRecentActivity} width="30%" height={32} borderRadius={8} duration={1000}>
                  <Text style={[styles.subHeading]}>
                    {
                      recentActivity?.activityType || "Not logged yet"
                    }
                  </Text>
                </SkeletonItem>
                <SkeletonItem isLoading={isLoadingRecentActivity} width="30%" height={16} borderRadius={4}>
                  {
                    <FormattedTime style={styles.lastLogTimeText} timestamp={recentActivity?.updatedAt} prefix="Today at" />
                  }
                </SkeletonItem>
              </View>
              <ActivityLedgerCard />
              <CustomButton
                title="Add Activity"
                style={{ alignSelf: "flex-end", width: 120, marginTop: 16 }}
                onPress={() => {
                  navigation.navigate("Edit Activities");
                }}
                isLoading={isLoadingActivityLedger}
              />
            </View>

            <HighlightCard highlightData={activityHighlights} isLoading={isLoadingActivityHighlights} />

            {/* Exercise Selection */}
            <View style={{ gap: 18, marginHorizontal: 4 }}>
              <View style={{ marginTop: 24, gap: 16 }}>
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "flex-end",
                    marginHorizontal: 16,
                  }}
                >
                  <Text style={[styles.subHeading, { marginHorizontal: 8 }]}>
                    Suggested
                  </Text>
                  <Text style={styles.popularText}>Popular</Text>
                </View>
                <VideoOverviewCard
                  video={videoData[0]}
                  handlePlayVideo={handlePlayVideo}
                />
              </View>

              <View style={{ marginTop: 24 }}>
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "flex-end",
                    marginHorizontal: 16,
                  }}
                >
                  <Text style={[styles.headerTitle, { marginHorizontal: 0 }]}>
                    More Workouts
                  </Text>
                </View>

                {/* Pilates Section */}
                <View style={{ marginTop: 8 }}>
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "flex-end",
                      marginHorizontal: 16,
                      marginBottom: 12,
                    }}
                  >
                    <Text style={styles.subHeading}>Pilates</Text>
                    <Text style={styles.popularText}>Popular</Text>
                  </View>
                  <VideoOverviewCard
                    video={videoData[1]}
                    handlePlayVideo={handlePlayVideo}
                  />
                </View>

                {/* Other Exercise Section */}
                <View style={{ marginTop: 24 }}>
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "flex-end",
                      marginHorizontal: 16,
                      marginBottom: 12,
                    }}
                  >
                    <Text style={styles.subHeading}>Abs</Text>
                    <Text style={styles.popularText}>New</Text>
                  </View>
                  <VideoOverviewCard
                    video={videoData[2]}
                    handlePlayVideo={handlePlayVideo}
                  />
                </View>

                {/* Strength Training Section */}
                <View style={{ marginTop: 24 }}>
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent: "space-between",
                      alignItems: "flex-end",
                      marginHorizontal: 16,
                      marginBottom: 12,
                    }}
                  >
                    <Text style={styles.subHeading}>Strength</Text>
                    <Text style={styles.popularText}>Trending</Text>
                  </View>
                  <VideoOverviewCard
                    video={videoData[3]}
                    handlePlayVideo={handlePlayVideo}
                  />
                </View>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

export default ActivityScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: "column",
    marginBottom: 80,
    top: 10,
  },
  header: {
    // marginHorizontal: 8,
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 35,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
    marginHorizontal: 16,
  },
  subHeading: {
    fontSize: 25,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_500,
  },
  lastLogTimeText: {
    fontSize: 12,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_500,
  },
  popularText: {
    backgroundColor: Colors.primaryGreen,
    fontSize: 18,
    color: Colors.white,
    padding: 2,
    paddingHorizontal: 20,
    borderRadius: 50,
    fontFamily: ThemeFonts.Exo_400,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
  workoutBgImage: {
    height: 225,
    borderRadius: 25,
  },
  workoutContainer: {
    position: "absolute",
    right: 12,
    left: 12,
    bottom: 12,
    backgroundColor: Colors.veryLightGreen,
    padding: 16,
    borderRadius: 25,
    flexDirection: "row",
    alignItems: "flex-end",
  },
  recommendedTag: {
    fontSize: 10,
    fontFamily: ThemeFonts.Exo_400,
    backgroundColor: Colors.primaryGreen,
    color: Colors.white,
    paddingHorizontal: 8,
    borderRadius: 10,
    paddingVertical: 2,
  },
  activityMetric: {
    alignItems: "center",
    flex: 1,
  },
  metricValue: {
    fontSize: 24,
    fontFamily: ThemeFonts.Lexend_700,
    color: Colors.white,
  },
  metricLabel: {
    fontSize: 13,
    fontFamily: ThemeFonts.Exo_400,
    color: Colors.white,
    marginTop: 4,
  },
  verticalDivider: {
    width: 1,
    height: 50,
    backgroundColor: Colors.white,
    marginHorizontal: 8,
  },
});
