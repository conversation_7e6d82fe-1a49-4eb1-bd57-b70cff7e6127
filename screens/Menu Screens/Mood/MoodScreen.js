import React, { useState, useCallback, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableWithoutFeedback,
  TouchableOpacity,
  Platform,
  RefreshControl,
  FlatList,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { Colors } from "constants/theme/colors";
import { ThemeFonts } from "constants/theme/fonts";
import CustomSelectWithLabel from "components/CustomAction/CustomSelectWithLabel";
import { CustomButton, CustomLoader } from "components/CustomAction";
import CustomAlert from "components/CustomAction/CustomAlert";
import YouTubePlayerModal from "components/CustomVideo/YouTubePlayerModal";
import { getYouTubeVideoId, getYouTubeThumbnail } from "utils/youtube";
import FormattedTime from "components/CustomText/FormattedTime";
import VideoOverviewCard from "components/CustomCards/VideoOverviewCard";
import moodService from "services/moodService";
import FlatListBottomLoader from "components/Loaders/FlatListBottomLoader";
import MoodLineGraph from "components/Charts/LineGraphs/MoodLineGraph";
import HighlightCard from "components/CustomCards/HighlightCard";
import useMoodStore from "store/moodStore";
import MoodLineGraph2 from "components/Charts/LineGraphs/MoodLineGraph2";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";

const MoodScreen = () => {
  const {
    isLoadingMoodData,
    isLoadingLastLoggedMood,
    isLoadingRecommendedMoodVideo,
    isLoadingMoodHighlights,
  } = useMoodStore((state) => state);

  const {
    lastLoggedMood,
    recommendedMoodVideo,
    moodGraphData,
    moodHighlights,
  } = useMoodStore((state) => state);

  const {
    setLoadingMoodStore,
    saveMoodData,
    getMoodLastLogged,
    getMoodRecommendedVideo,
    getMoodGraphData,
    getMoodHighlights,
    clearMoodStoreErrors,
  } = useMoodStore((state) => state);

  const {
    savingMoodError,
    lastLoggedMoodError,
    recommendedMoodVideoError,
    moodGraphError,
    moodHighlightsError,
  } = useMoodStore((state) => state);

  const [isLoadingOptions, setIsLoadingOptions] = useState(true);
  const [MoodOptions, setMoodOptions] = useState([]);
  const [HungerOptions, setHungerOptions] = useState([]);

  const [isLoadingMoreVideos, setIsLoadingMoreVideos] = useState(true);

  const [moreVideos, setMoreVideos] = useState([]);
  const [currentVideoPage, setCurrentVideoPage] = useState(1);
  const [hasMoreVideos, setHasMoreVideos] = useState(true);

  const [selectedMood, setSelectedMood] = useState(null);
  const [selectedHunger, setSelectedHunger] = useState(null);

  const [saveSelectedMood, setSaveSelectedMood] = useState(null);
  const [saveSelectedHunger, setSaveSelectedHunger] = useState(null);

  const [validationError, setValidationError] = useState({});
  const [successMessage, setSuccessMessage] = useState(null);

  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
  const [showVideo, setShowVideo] = useState(false);

  const [isInSaveMode, setIsInSaveMode] = useState(false);

  const [currentVideo, setCurrentVideo] = useState({
    id: "",
    title: "",
    description: "",
  });

  const insets = useSafeAreaInsets();

  useEffect(() => {
    setIsLoadingOptions(true);
    (async () => {
      const getMoodOptionsRes = await moodService.getMoodOptions();
      const getHungerOptionsRes = await moodService.getHungerOptions();

      if (getMoodOptionsRes.success && getHungerOptionsRes.success) {
        setMoodOptions(getMoodOptionsRes.data);
        setHungerOptions(getHungerOptionsRes.data);
      }
      setIsLoadingOptions(false);
    })();
  }, []);

  useEffect(() => {
    // setLoadingMoodStore(true);
    (async () => {
      getMoodLastLogged();
      getMoodRecommendedVideo();
      getMoodHighlights();
      getMoodGraphData();
      // setLoadingMoodStore(false);
    })();
  }, []);

  useEffect(() => {
    if (lastLoggedMood) {
      setSelectedMood(
        lastLoggedMood.moodType
          .split(" ")
          .map(
            (word) => word[0].toUpperCase() + word.substring(1).toLowerCase()
          )
          .join(" ")
      );
      setSelectedHunger(
        lastLoggedMood.hungerLevel
          .split(" ")
          .map(
            (word) => word[0].toUpperCase() + word.substring(1).toLowerCase()
          )
          .join(" ")
      );
    }
  }, [lastLoggedMood]);

  const getAllVideos = async () => {
    setIsLoadingMoreVideos(true);
    const getAllVideosRes = await moodService.getAllVideos({
      page: currentVideoPage,
    });

    if (getAllVideosRes.success) {
      setMoreVideos((prevData) => [...prevData, ...getAllVideosRes.data]);
      setHasMoreVideos(getAllVideosRes.data.length > 0);
      setCurrentVideoPage((prevPage) => prevPage + 1);
    } else {
      setHasMoreVideos(false);
    }

    setIsLoadingMoreVideos(false);
  };

  const handleLoadMoreVideos = () => {
    if (!isLoadingMoreVideos && hasMoreVideos) {
      getAllVideos();
    }
  };

  useEffect(() => {
    getAllVideos();
  }, []);

  const validateForm = () => {
    if (!selectedMood) {
      setValidationError({
        mood: "Please select a mood",
      });
      return false;
    }
    if (!selectedHunger) {
      setValidationError({
        hunger: "Please select a hunger level",
      });
      return false;
    }
    return true;
  };

  const isWithinLastHour = () => {
    if (!lastLoggedMood || !lastLoggedMood?.updatedAt) return false;

    const createdAtTime = new Date(lastLoggedMood?.updatedAt).getTime();

    const now = Date.now();

    return now - createdAtTime <= 60 * 60 * 1000;
  };

  const handleEditMode = () => {
    setIsInSaveMode(true);
    setSaveSelectedMood(selectedMood);
    setSaveSelectedHunger(selectedHunger);
  };

  const handleSaveMoodData = async () => {
    if (!validateForm()) return;

    setValidationError({});

    if (
      isWithinLastHour() &&
      lastLoggedMood &&
      selectedMood.toLowerCase() === lastLoggedMood.moodType.toLowerCase() &&
      selectedHunger.toLowerCase() === lastLoggedMood.hungerLevel.toLowerCase()
    ) {
      setIsInSaveMode(false);
      return;
    }

    setLoadingMoodStore(true);
    const successSaveMood = await saveMoodData({
      mood: selectedMood,
      hungerLevel: selectedHunger,
    });

    if (successSaveMood) {
      await getMoodLastLogged();
      getMoodRecommendedVideo();
      getMoodGraphData();
      getMoodHighlights();
      setSuccessMessage("Mood saved successfully");
    }

    setIsInSaveMode(false);
    setLoadingMoodStore(false);
    setCurrentOpenDropdown(null);
  };

  const handlePlayVideo = (video) => {
    const videoId = getYouTubeVideoId(video.videoUrl);
    if (videoId) {
      setCurrentVideo({
        id: videoId,
        title: video.title,
        description: video.description,
      });
      setShowVideo(true);
    }
  };

  const onRefresh = useCallback(async () => {
    // setLoadingMoodStore(true);
    setIsInSaveMode(false);

    getMoodLastLogged();
    getMoodRecommendedVideo();
    getMoodHighlights();
    getMoodGraphData();

    // setLoadingMoodStore(false);
  }, []);

  if (showVideo) {
    return (
      <YouTubePlayerModal
        videoId={currentVideo.id}
        title={currentVideo.title}
        description={currentVideo.description}
        onClose={() => {
          setShowVideo(false);
          setCurrentVideo({ id: "", title: "", description: "" });
        }}
        topInset={insets.top}
      />
    );
  }

  // if (isLoadingMoodData) {
  //   return (
  //     <AppLayout>
  //       <View style={styles.loaderContainer}>
  //         <CustomLoader />
  //       </View>
  //     </AppLayout>
  //   );
  // }

  return (
    <AppLayout>
      <ScrollView
        showsVerticalScrollIndicator={false}
        nestedScrollEnabled
        scrollEventThrottle={16}
        contentContainerStyle={{
          paddingBottom: isLoadingMoreVideos && hasMoreVideos ? 120 : 90,
        }}
        refreshControl={
          <RefreshControl
            refreshing={isLoadingMoodData}
            onRefresh={onRefresh}
            colors={[Colors.primaryGreen]}
            tintColor={Colors.primaryGreen}
            progressViewOffset={24}
          />
        }
        onScroll={(e) => {
          let paddingToBottom = 0;
          paddingToBottom += e.nativeEvent.layoutMeasurement.height;
          if (
            e.nativeEvent.contentOffset.y >=
            e.nativeEvent.contentSize.height - paddingToBottom
          ) {
            handleLoadMoreVideos();
          }
        }}
      >
        <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
          <View style={[styles.container]}>
            <CustomAlert
              visible={
                !!savingMoodError ||
                !!lastLoggedMoodError ||
                !!recommendedMoodVideoError ||
                !!moodGraphError ||
                !!moodHighlightsError
              }
              title="Error"
              message={
                savingMoodError ||
                lastLoggedMoodError ||
                recommendedMoodVideoError ||
                moodGraphError ||
                moodHighlightsError
              }
              buttons={[
                {
                  text: "OK",
                  onPress: () => {
                    clearMoodStoreErrors();
                  },
                  style: "allowButton",
                },
              ]}
              onClose={() => {
                clearMoodStoreErrors();
              }}
            />
            {successMessage && (
              <CustomAlert
                visible={!!successMessage}
                title="Success"
                message={successMessage}
                buttons={[
                  {
                    text: "OK",
                    onPress: () => setSuccessMessage(null),
                    style: "allowButton",
                  },
                ]}
                onClose={() => setSuccessMessage(null)}
              />
            )}
            <View style={styles.header}>
              <Text style={styles.headerTitle}>Mood</Text>
              <View style={styles.headerContent}>
                <View style={styles.headerRow}>
                  <SkeletonItem
                    height={20}
                    width={"30%"}
                    isLoading={isLoadingLastLoggedMood || isLoadingMoodData}
                    style={{ marginTop: 4 }}
                  >
                    <Text style={styles.subHeading}>
                      {lastLoggedMood
                        ? ` ${lastLoggedMood?.moodType}`
                        : "Log Your Mood"}
                    </Text>
                  </SkeletonItem>
                  <SkeletonItem
                    height={16}
                    width={"30%"}
                    isLoading={isLoadingLastLoggedMood || isLoadingMoodData}
                    style={{ marginTop: 4 }}
                  >
                    <FormattedTime
                      timestamp={lastLoggedMood?.updatedAt}
                      style={styles.lastLogTimeText}
                    />
                  </SkeletonItem>
                </View>
              </View>
            </View>

            <View style={{ gap: 32 }}>
              <CustomSelectWithLabel
                options={MoodOptions?.filter(
                  (option) => option.value !== "all"
                )}
                label="Enter Mood"
                separateLabel="How are you feeling right now?"
                selectedValue={selectedMood}
                onValueChange={(value) => setSelectedMood(value)}
                isEditing={isInSaveMode}
                showSaveIcon={true}
                triggerZ={20}
                listZ={19}
                labelStyle={{
                  color: Colors.veryDarkGreen,
                  backgroundColor: Colors.veryLightGreen,
                  paddingVertical: 20,
                }}
                currentOpenDropdown={currentOpenDropdown}
                dropdownId={"mood_dropdown"}
                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                clearValidationError={() =>
                  setValidationError((prev) => ({ ...prev, mood: "" }))
                }
                error={validationError?.mood}
                disabledSelectColor={Colors.primaryPurple}
                isLoading={
                  isLoadingLastLoggedMood ||
                  isLoadingOptions ||
                  isLoadingMoodData
                }
              />

              <CustomSelectWithLabel
                options={HungerOptions?.filter(
                  (option) => option.value !== "all"
                )}
                label="Enter Hunger Level"
                separateLabel="How hungry are you right now?"
                selectedValue={selectedHunger}
                onValueChange={(value) => setSelectedHunger(value)}
                isEditing={isInSaveMode}
                showSaveIcon={true}
                triggerZ={18}
                listZ={17}
                labelStyle={{
                  color: Colors.veryDarkGreen,
                  backgroundColor: Colors.veryLightGreen,
                  paddingVertical: 20,
                }}
                currentOpenDropdown={currentOpenDropdown}
                dropdownId={"hunger_dropdown"}
                setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                clearValidationError={() =>
                  setValidationError((prev) => ({ ...prev, hunger: "" }))
                }
                error={validationError?.hunger}
                isLoading={
                  isLoadingLastLoggedMood ||
                  isLoadingOptions ||
                  isLoadingMoodData
                }
              />

              {!isInSaveMode ? (
                <CustomButton
                  title={isWithinLastHour() ? "Edit" : "Add"}
                  style={{ alignSelf: "flex-end", width: 92 }}
                  onPress={handleEditMode}
                  disabled={
                    isLoadingLastLoggedMood ||
                    isLoadingOptions ||
                    isLoadingMoodData
                  }
                  disabledBgColor={Colors.lightGray}
                  disabledTextColor={Colors.darkGray}
                  isLoading={
                    isLoadingMoodData ||
                    isLoadingLastLoggedMood ||
                    isLoadingOptions
                  }
                />
              ) : (
                <View
                  style={{
                    flex: 1,
                    flexDirection: "row",
                    justifyContent: "space-between",
                  }}
                >
                  <CustomButton
                    title={"Cancel"}
                    onPress={() => {
                      setCurrentOpenDropdown(null);
                      setIsInSaveMode(false);
                      setSelectedMood(saveSelectedMood);
                      setSelectedHunger(saveSelectedHunger);
                      setValidationError({});
                    }}
                    style={
                      isLoadingMoodData || isLoadingLastLoggedMood
                        ? { width: 92 }
                        : {
                            width: 92,
                            backgroundColor: Colors.white,
                            borderWidth: 2,
                            borderColor: Colors.primaryPurple,
                          }
                    }
                    textColor={Colors.primaryPurple}
                    disabled={
                      isLoadingLastLoggedMood ||
                      isLoadingOptions ||
                      isLoadingMoodData
                    }
                    isLoading={
                      isLoadingMoodData ||
                      isLoadingLastLoggedMood ||
                      isLoadingOptions
                    }
                  />
                  <CustomButton
                    title={"Save"}
                    style={{ alignSelf: "flex-end", width: 92 }}
                    onPress={handleSaveMoodData}
                    disabled={
                      isLoadingLastLoggedMood ||
                      isLoadingOptions ||
                      isLoadingMoodData
                    }
                    isLoading={
                      isLoadingMoodData ||
                      isLoadingLastLoggedMood ||
                      isLoadingOptions
                    }
                  />
                </View>
              )}
            </View>
            {/* Recommended Video */}
            <View
              style={{
                margin: 32,
                marginHorizontal: 0,
                marginBottom: 16,
                zIndex: 1,
              }}
            >
              <Text
                style={[
                  styles.headerTitle,
                  { marginHorizontal: 16, marginBottom: 8 },
                ]}
              >
                Watch this!
              </Text>
              <SkeletonItem
                height={224}
                width={"100%"}
                borderRadius={25}
                isLoading={isLoadingRecommendedMoodVideo || isLoadingMoodData}
              >
                {!recommendedMoodVideo ? (
                  <View style={styles.noVideoContainer}>
                    <Text style={styles.noVideoText}>
                      No recommended video available
                    </Text>
                  </View>
                ) : (
                  <VideoOverviewCard
                    video={recommendedMoodVideo}
                    handlePlayVideo={handlePlayVideo}
                  />
                )}
              </SkeletonItem>
            </View>

            {/* Mood Graphs */}
            {moodGraphData.length > 0 && (
              <View style={styles.graphContainer}>
                <MoodLineGraph2
                  currentOpenDropdown={currentOpenDropdown}
                  setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                />
              </View>
            )}

            <HighlightCard
              highlightData={moodHighlights}
              isLoading={isLoadingMoodHighlights || isLoadingMoodData}
            />

            {/* More Videos */}
            <View style={styles.moreVideosContainer}>
              <Text style={[styles.headerTitle, { marginHorizontal: 16 }]}>
                More Videos
              </Text>

              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "flex-end",
                  marginHorizontal: 16,
                  marginVertical: 16,
                }}
              >
                <Text style={styles.subHeading}>Strength</Text>
                <Text style={styles.popularText}>Trending</Text>
              </View>

              <FlatList
                data={moreVideos}
                keyExtractor={(item) => item.id}
                scrollEnabled={false}
                contentContainerStyle={{ gap: 32 }}
                renderItem={({ item }) => (
                  <VideoOverviewCard
                    video={item}
                    handlePlayVideo={handlePlayVideo}
                  />
                )}
                ListFooterComponent={
                  isLoadingMoreVideos && hasMoreVideos ? (
                    <FlatListBottomLoader />
                  ) : null
                }
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

export default MoodScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: "column",
    top: 10,
  },
  header: {
    marginHorizontal: 24,
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 35,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
  },
  headerContent: {
    marginTop: 8,
    // width: screenWidth,
    // marginHorizontal: 2,
  },
  headerRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  subHeading: {
    fontSize: 18,
    left: -4,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_500,
    textTransform: "capitalize",
  },
  lastLogTimeText: {
    fontSize: 12,
    color: Colors.black,
    textAlign: "start",
    alignSelf: "flex-end",
    fontFamily: ThemeFonts.Exo_500,
    marginLeft: 10,
    textDecorationLine: "underline",
  },
  popularText: {
    backgroundColor: Colors.primaryGreen,
    fontSize: 14,
    color: Colors.white,
    padding: 2,
    paddingHorizontal: 20,
    borderRadius: 50,
    fontFamily: ThemeFonts.Exo_400,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
  recommendedTag: {
    fontSize: 10,
    fontFamily: ThemeFonts.Exo_400,
    backgroundColor: Colors.primaryGreen,
    color: Colors.white,
    paddingHorizontal: 8,
    borderRadius: 10,
    paddingVertical: 2,
  },
  feelGoodBgImage: {
    height: 225,
    borderRadius: 25,
    width: "100%",
    resizeMode: "cover",
  },
  soundTherapyContainer: {
    position: "absolute",
    right: 12,
    left: 12,
    bottom: 12,
    backgroundColor: Colors.veryLightGreen,
    padding: 16,
    // paddingVertical: 12,
    borderRadius: 25,
    flexDirection: "row",
    alignItems: "flex-end",
  },
  graphContainer: {
    marginVertical: 24,
    // paddingHorizontal: 8,
  },
  graphWrapper: {
    // backgroundColor: Colors.veryLightGreen,
    borderRadius: 25,
    marginTop: 16,
  },
  graphTitle: {
    fontSize: 18,
    fontFamily: ThemeFonts.Exo_600,
    color: Colors.black,
    marginBottom: 12,
  },
  moreVideosContainer: {
    marginTop: 24,
    // marginHorizontal: 6,
  },
  noVideoContainer: {
    backgroundColor: Colors.veryLightGreen,
    padding: 24,
    borderRadius: 25,
    marginTop: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  noVideoText: {
    fontSize: 16,
    fontFamily: ThemeFonts.Exo_500,
    color: Colors.black,
    textAlign: "center",
  },
  description: {
    fontSize: 14,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_400,
    marginBottom: 10,
    textTransform: "capitalize",
  },
  videoTitle: {
    fontSize: 19,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_500,
    textTransform: "capitalize",
  },
});
