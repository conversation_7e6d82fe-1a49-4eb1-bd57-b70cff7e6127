import { useNavigation } from "@react-navigation/native";
import { CustomAlert, CustomButton } from "components/CustomAction";
import CustomWeightInput from "components/CustomAction/CustomWeightInput";
import AppLayout from "navigations/components/Layouts/AppLayout";
import { screenHeight } from "constants/sizes";
import { Colors } from "constants/theme/colors";
import { ThemeFonts } from "constants/theme/fonts";
import React, {
  useState,
  useCallback,
  useRef,
  useEffect,
  useMemo,
} from "react";
import { TouchableWithoutFeedback } from "react-native";
import { StyleSheet, View, ScrollView, Text } from "react-native";
import { weightLossService } from "services/weightLossService";
import CustomSelectWithLabel from "components/CustomAction/CustomSelectWithLabel";
import { WEIGH_UNIT_OPTIONS } from "constants/constants";
import CustomCalender from "components/CustomPickers/CustomCalender";
import useUserWeightStore from "store/userWeightStore";

const RecordWeight = () => {
  const scrollViewRef = useRef();
  const navigation = useNavigation();

  const {
    setLoadingUserWeightStore,
    getLastLoggedWeightData,
    getWeightGraphData,
    getWeightHighlights,
    clearWeightStoreErrors,
  } = useUserWeightStore((state) => state);

  const [selected, setSelected] = useState(
    new Date().toISOString().split("T")[0]
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [selectedWeight, setSelectedWeight] = useState(null);
  const [existingRecord, setExistingRecord] = useState(null);
  const [logTime, setLogTime] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [savedWeight, setSavedWeight] = useState(null);
  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);
  const [validationError, setValidationError] = useState({});
  const [weightUnit, setWeightUnit] = useState("kg"); // Default to kg

  // Format time for display
  const formatTime = useCallback((timestamp) => {
    const createdAt = new Date(timestamp);
    return createdAt
      .toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      })
      .replace("am", "AM")
      .replace("pm", "PM");
  }, []);

  // Fetch weight record data
  const fetchWeightRecord = useCallback(
    async (date) => {
      setIsLoading(true);
      try {
        const recordResult = await weightLossService.getWeightRecordForDate(
          date
        );
        if (!recordResult.error && recordResult.data) {
          // Get weight from API (in kg) - we'll use this directly
          // We'll handle the conversion in the weight unit effect
          const weightStr = recordResult.data.weight.toFixed(1);

          // Set the existing record first
          setExistingRecord(recordResult.data);

          // Then update the display values
          setSelectedWeight(weightStr);
          setSavedWeight(weightStr);
          setIsEditing(false);
          setLogTime(
            `Last logged at ${formatTime(recordResult.data.createdAt)}`
          );
        } else {
          setExistingRecord(null);
          setSelectedWeight("");
          setLogTime("");
          setIsEditing(true);
        }
      } catch (err) {
        setError("Failed to fetch weight record");
      } finally {
        setIsLoading(false);
      }
    },
    [formatTime]
  );

  // Handle weight logging
  const handleLogWeight = useCallback(async () => {
    // Validate weight input
    if (!selectedWeight) {
      setValidationError({ weight: "Please enter your weight." });
      return;
    }

    const weightValue = parseFloat(selectedWeight);
    if (isNaN(weightValue) || weightValue <= 0) {
      setValidationError({ weight: "Please enter a valid weight." });
      return;
    }

    // Convert weight to kg if unit is lbs, otherwise keep as is
    const weightInKg =
      weightUnit === "lbs" ? weightValue * 0.453592 : weightValue;

    // Exit edit mode if no changes
    if (
      isEditing &&
      existingRecord &&
      weightValue === parseFloat(savedWeight)
    ) {
      setIsEditing(false);
      return;
    }

    setIsLoading(true);
    setValidationError({});

    try {
      let res;
      if (existingRecord) {
        // Update existing record
        res = await weightLossService.updateWeightData({
          id: existingRecord.id,
          weight: weightInKg, // Send weight in kg to API
          date: selected,
        });
      } else {
        // Create new record
        res = await weightLossService.logWeightData({
          date: selected,
          weight: weightInKg, // Send weight in kg to API
        });

        setIsLoading(false);
        // Update UI state with new record data
        if (res.success && res.data) {
          setExistingRecord(res.data);
          const formattedTime = formatTime(res.data.createdAt);
          setLogTime(`Last logged at ${formattedTime}`);
        }
      }

      setIsLoading(false);

      if (res.success) {
        getLastLoggedWeightData();
        getWeightGraphData();
        getWeightHighlights();
        setSuccess(res.message);
        setIsEditing(false);
      } else {
        setError(res.error);
      }
    } catch (err) {
      setError("Failed to save weight data");
      // setLoadingUserWeightStore(false);
    }
  }, [
    selected,
    selectedWeight,
    existingRecord,
    isEditing,
    savedWeight,
    formatTime,
    weightUnit,
  ]);

  // Render action buttons based on current state
  const renderActionButtons = useCallback(() => {
    if (!existingRecord) {
      return (
        <CustomButton
          title="Save"
          style={{ alignSelf: "flex-end", width: 89 }}
          onPress={handleLogWeight}
          disabled={isLoading}
          isLoading={isLoading}
        />
      );
    }

    if (isEditing) {
      return (
        <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
          <CustomButton
            title="Cancel"
            style={
              isLoading
                ? { width: 89 }
                : {
                  width: 89,
                  backgroundColor: Colors.white,
                  borderWidth: 2,
                  borderColor: Colors.primaryPurple,
                }
            }
            textColor={Colors.primaryPurple}
            onPress={() => {
              setIsEditing(false);
              setSelectedWeight(savedWeight);
              setValidationError({});
            }}
            disabled={isLoading}
            disabledBgColor={Colors.white}
            disabledTextColor={Colors.primaryPurple}
            isLoading={isLoading}
          />
          <CustomButton
            title="Save"
            style={{ width: 89 }}
            onPress={handleLogWeight}
            disabled={isLoading}
            isLoading={isLoading}
          />
        </View>
      );
    }

    return (
      <CustomButton
        title="Edit"
        style={{ alignSelf: "flex-end", width: 89 }}
        onPress={() => {
          setIsEditing(true);
          setSavedWeight(selectedWeight);
        }}
        isLoading={isLoading}
      />
    );
  }, [
    existingRecord,
    isEditing,
    savedWeight,
    handleLogWeight,
    selectedWeight,
    isLoading,
  ]);

  // Initialize component with current date's weight record
  useEffect(() => {
    fetchWeightRecord(selected);
  }, [selected, fetchWeightRecord]);

  // Handle weight unit changes without re-fetching
  useEffect(() => {
    if (existingRecord && existingRecord.weight) {
      // Instead of re-fetching, just convert the existing weight value
      let displayWeight = existingRecord.weight; // Weight is in kg from API

      // Only convert to lbs if user selected lbs
      if (weightUnit === "lbs") {
        displayWeight = existingRecord.weight * 2.20462; // Convert kg to lbs
      }

      const weightStr = displayWeight.toFixed(1);
      setSelectedWeight(weightStr);
      setSavedWeight(weightStr);
    }
  }, [weightUnit, existingRecord]);

  return (
    <AppLayout bgColor={Colors.primaryGreen} paddingHorizontal={0}>
      <ScrollView showsVerticalScrollIndicator={false} ref={scrollViewRef}>
        <TouchableWithoutFeedback onPress={() => setCurrentOpenDropdown(null)}>
          <View style={styles.container}>
            <CustomCalender
              heading="Weight Loss"
              totalDays={30}
              currentMonth={selected}
              setCurrentMonth={(value) => setSelected(value)}
              currentOpenDropdown={currentOpenDropdown}
              setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
            />
            <View style={styles.section2}>
              <View style={styles.header}>
                <Text style={styles.recordWeight}>Record Weight</Text>
                {/* {logTime ? (
                                    <Text style={styles.logTimeText}>{logTime}</Text>
                                ) : null} */}
              </View>

              <View style={styles.section1}>
                <CustomSelectWithLabel
                  options={WEIGH_UNIT_OPTIONS}
                  label="Select Weight Unit"
                  selectedValue={weightUnit}
                  onValueChange={(value) => setWeightUnit(value)}
                  triggerZ={10}
                  listZ={9}
                  currentOpenDropdown={currentOpenDropdown}
                  dropdownId={1}
                  setCurrentOpenDropdown={(id) => setCurrentOpenDropdown(id)}
                  error={validationError?.weightUnitError}
                  isEditing={isEditing || !existingRecord}
                  clearValidationError={() => {
                    setValidationError((prev) => ({
                      ...prev,
                      weightUnitError: "",
                    }));
                  }}
                  disabledSelectColor={Colors.white}
                  disabledTextColor={Colors.black}
                  isLoading={isLoading}
                />
                <CustomWeightInput
                  label={`Log Weight`}
                  placeholder={`Enter weight in ${weightUnit}`}
                  value={selectedWeight?.toString() || ""}
                  onChangeText={setSelectedWeight}
                  error={validationError?.weight}
                  rightText={weightUnit}
                  keyboardType="number-pad"
                  editable={isEditing || !existingRecord}
                  clearValidationError={() => {
                    setValidationError((prev) => ({ ...prev, weight: "" }));
                  }}
                  isLoading={isLoading}
                />
              </View>
              {renderActionButtons()}
            </View>
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
      <CustomAlert
        visible={!!error || !!success}
        title={error ? "Error" : "Success"}
        message={error || success}
        buttons={[
          {
            text: "OK",
            onPress: () => {
              setError(null);
              setSuccess(null);
              if (success) {
                setTimeout(() => {
                  navigation.goBack();
                }, 0);
              }
            },
            style: "allowButton",
          },
        ]}
        onClose={() => {
          setError(null);
          setSuccess(null);
          if (success) {
            setTimeout(() => {
              navigation.goBack();
            }, 0);
          }
        }}
      />
    </AppLayout>
  );
};

export default RecordWeight;

const styles = StyleSheet.create({
  container: {
    // marginHorizontal: 16
  },
  section2: {
    flex: 1,
    flexDirection: "column",
    gap: 24,
    backgroundColor: Colors.white,
    borderTopRightRadius: 50,
    borderTopLeftRadius: 50,
    padding: 16,
    marginTop: 16,
    paddingBottom: 90,
  },
  header: {
    marginHorizontal: 22,
    top: 10,
  },
  recordWeight: {
    fontSize: 32,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
  },
  logTimeText: {
    fontSize: 14,
    color: Colors.darkGray,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_400,
    marginTop: 4,
  },
  section1: {
    marginTop: 16,
    marginBottom: 16,
    gap: 15,
  },
  loaderContainer: {
    flex: 1,
    top: 15,
    justifyContent: "center",
    marginHorizontal: "auto",
    alignItems: "center",
    zIndex: 9998,
  },
});
