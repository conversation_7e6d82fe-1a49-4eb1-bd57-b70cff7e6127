import {
  FlatList,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import React, { useCallback, useEffect, useState } from "react";
import { ThemeFonts } from "constants/theme/fonts";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { Colors } from "constants/theme/colors";
import {
  CustomAlert,
  CustomButton,
  CustomLoader,
} from "components/CustomAction";
import { weightLossService } from "services/weightLossService";
import { screenWidth } from "constants/sizes";
import { format } from "date-fns";
import AppLayout from "navigations/components/Layouts/AppLayout";
import useUserWeightStore from "store/userWeightStore";
import HighlightCard from "components/CustomCards/HighlightCard";
import WeightLineGraph from "components/Charts/LineGraphs/WeightLineGraph";
import getRecentLogTime from "utils/dateandtimeformatters/getRecentLogTime";
import SkeletonItem from "components/CustomSkeltonLoader/AdvancedSkeletonLoader";

const WeightScreen = () => {
  const navigation = useNavigation();

  const {
    isLoadingUserWeightStore,
    isLoadingLastLoggedWeightData,
    isLoadingWeightHighlights,
  } = useUserWeightStore((state) => state);
  const { lastLoggedWeight, weightHighlights } = useUserWeightStore(
    (state) => state
  );

  const {
    setLoadingUserWeightStore,
    getLastLoggedWeightData,
    getWeightGraphData,
    getWeightHighlights,
    clearWeightStoreErrors,
  } = useUserWeightStore((state) => state);

  const { lastLoggedWeightError, weightGraphError, weightHighlightsError } =
    useUserWeightStore((state) => state);

  const [currentOpenDropdown, setCurrentOpenDropdown] = useState(null);

  useEffect(() => {
    (async () => {
      getLastLoggedWeightData();
      getWeightGraphData();
      getWeightHighlights();
    })();
  }, []);

  return (
    <AppLayout>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isLoadingUserWeightStore}
            onRefresh={async () => {
              getLastLoggedWeightData();
              getWeightGraphData();
              getWeightHighlights();
            }}
            colors={[Colors.primaryGreen]}
            progressViewOffset={24}
          />
        }
        contentContainerStyle={{ flexGrow: 1 }}
      >
        <TouchableWithoutFeedback>
          <View
            style={{
              flex: 1,
              flexDirection: "column",
              gap: 24,
              marginBottom: 80,
            }}
          >
            <View style={styles.header}>
              <Text style={styles.headerTitle}>Weight Loss</Text>
              <SkeletonItem
                height={28}
                width={"60%"}
                isLoading={isLoadingLastLoggedWeightData}
                style={{ marginTop: 0, marginHorizontal: 14 }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "flex-end",
                  }}
                >
                  {!lastLoggedWeightError && lastLoggedWeight ? (
                    <>
                      <Text style={styles.totalWeightText}>
                        {`${getRecentLogTime(
                          lastLoggedWeight.updatedAt,
                          lastLoggedWeight.date
                        )} : ${
                          lastLoggedWeight?.weight
                            ? Number(lastLoggedWeight.weight).toFixed(1)
                            : 0
                        } kg`}
                      </Text>
                    </>
                  ) : (
                    <>
                      <Text style={styles.totalWeightText}>
                        {`Today at ${new Date()
                          .toLocaleTimeString("en-US", {
                            hour: "2-digit",
                            minute: "2-digit",
                            hour12: true,
                          })
                          .replace("am", "AM")
                          .replace("pm", "PM")} : 0 kg`}
                      </Text>
                    </>
                  )}
                </View>
              </SkeletonItem>
              <View style={styles.headerContent}>
                <CustomButton
                  title="Log Weight"
                  onPress={() => {
                    navigation.navigate("Record_Weight");
                  }}
                  style={styles.logWeightBtn}
                  fontFamily={ThemeFonts.Exo_600}
                />
              </View>
            </View>
            <View style={{ marginHorizontal: 0 }}>
              <WeightLineGraph
                currentOpenDropdown={currentOpenDropdown}
                setCurrentOpenDropdown={setCurrentOpenDropdown}
              />
            </View>
            <HighlightCard
              isLoading={isLoadingWeightHighlights}
              highlightData={weightHighlights}
            />
            <CustomAlert
              visible={
                !!lastLoggedWeightError ||
                !!weightGraphError ||
                !!weightHighlightsError
              }
              title="Error"
              message={
                lastLoggedWeightError ||
                weightGraphError ||
                weightHighlightsError
              }
              buttons={[
                {
                  text: "OK",
                  onPress: () => {
                    clearWeightStoreErrors();
                  },
                  style: "allowButton",
                },
              ]}
              onClose={() => {
                clearWeightStoreErrors();
              }}
            />
          </View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </AppLayout>
  );
};

export default WeightScreen;

const styles = StyleSheet.create({
  header: {
    // marginHorizontal: 8,
    top: 10,
  },
  headerTitle: {
    fontSize: 35,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
    marginHorizontal: 14,
  },
  totalWeightText: {
    fontSize: 20,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_600,
    marginHorizontal: 14,
  },
  lastLogTimeText: {
    fontSize: 12,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_500,
    textDecorationLine: "underline",
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "flex-end",
    // justifyContent: "space-between",
    alignItems: "center",
    marginTop: 8,
    marginHorizontal: 8,
    // height: 36,
    marginBottom: 12,
  },
  logWeightBtn: {
    width: "auto",
    // width: screenWidth * 0.4,
    // marginTop: 16,
    alignSelf: "flex-end",
    paddingHorizontal: 32,
  },
  section1: {
    backgroundColor: Colors.lightGreen,
    marginHorizontal: 8,
    padding: 22,
    borderRadius: 25,
    paddingTop: 16,
  },
  section1Heading: {
    fontSize: 19,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_700,
  },
  highLightsText: {
    fontSize: 18,
    color: Colors.black,
    textAlign: "start",
    fontFamily: ThemeFonts.Exo_400,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
  chartContainer: {
    backgroundColor: Colors.white,
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  chartTitle: {
    fontSize: 18,
    fontFamily: ThemeFonts.Exo_600,
    color: Colors.black,
    marginBottom: 10,
  },
});
