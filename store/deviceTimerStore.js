import { create } from "zustand";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { persist, createJSONStorage } from "zustand/middleware";
import { deviceTimerService } from "../services/deviceTimerService";
import { highlightsService } from "services/highlightService";
import { backgroundTimerService } from "../services/backgroundTimerService";

const STORAGE_KEY = "device-timer-data";

const useDeviceTimerStore = create(
  persist(
    (set, get) => ({
      timerDuration: 3,
      isTimerRunning: false,
      isPaused: false,
      timerStartTime: null,
      timerEndTime: null,
      pauseStartTime: null,
      totalPausedTime: 0,
      remainingTime: 3 * 60,
      currentTimerSessionId: null,
      lastSyncTime: null,

      isLoadingTimerHistory: false,
      timerHistory: [],
      totalUserTime: 0,
      lastDeviceLogTime: null,
      timerHistoryError: null,

      isLoadingDeviceGraphRecords: false,
      deviceTimeGraphFilter: "weekly",
      deviceTimeGraphData: [],
      deviceTimeGraphTimePeriod: null,
      deviceTimeGraphError: null,

      isLoadingDeviceTimerHighlights: false,
      deviceTimerHighlights: [],
      deviceTimerHighlightsError: null,

      // Timer actions
      setTimerDuration: (duration) =>
        set({ timerDuration: duration, remainingTime: duration * 60 }),
      // Add More Timer actions
      setAddMoreTimerDuration: (duration) =>
        set({ timerDuration: duration, remainingTime: duration * 60 }),

      // Update timer duration and sync with backend
      updateTimerDuration: async (newDuration) => {
        const { isTimerRunning, timerStartTime, timerDuration, remainingTime } =
          get();

        // Safety check: ensure new duration is not less than current duration
        if (isTimerRunning && newDuration < timerDuration) {
          console.error("Cannot reduce timer duration while timer is running");
          return;
        }

        // Calculate the difference between the new and old duration
        const durationDifferenceInMinutes = newDuration - timerDuration;
        const durationDifferenceInSeconds = durationDifferenceInMinutes * 60;

        // Calculate the new remaining time by adding the difference to the current remaining time
        const newRemainingTime = Math.max(
          0,
          remainingTime + durationDifferenceInSeconds
        );

        // Calculate the total duration for backend tracking
        // This is the original duration plus the added time
        const totalDuration = timerDuration + durationDifferenceInMinutes;

        // Update the timer duration and remaining time in the store
        set({
          timerDuration: totalDuration,
          remainingTime: newRemainingTime,
          timerEndTime: timerStartTime
            ? new Date(new Date().getTime() + newRemainingTime * 1000)
            : null,
        });

        // If timer is running, sync the updated duration with the backend
        if (isTimerRunning && timerStartTime) {
          try {
            await deviceTimerService.createTimer({
              durationSet: totalDuration * 60, // Convert minutes to seconds for the API
              startTime: timerStartTime,
            });

            // Update last sync time
            set({ lastSyncTime: new Date() });

            console.log("Timer duration updated and synced with backend");
          } catch (error) {
            console.error(
              "Failed to sync updated timer duration with backend:",
              error
            );
          }
        }
      },

      startTimer: async () => {
        const now = new Date();
        const endTime = new Date(
          now.getTime() + get().timerDuration * 60 * 1000
        );

        set({
          isTimerRunning: true,
          isPaused: false,
          timerStartTime: now,
          timerEndTime: endTime,
          remainingTime: get().timerDuration * 60,
          totalPausedTime: 0,
          pauseStartTime: null,
          currentTimerSessionId: null, // Reset session ID for new timer
          lastSyncTime: now,
        });
        try {
          const response = await deviceTimerService.createTimer({
            startTime: now,
            durationSet: get().timerDuration * 60,
          });
          if (response.success && response.data) {
            set({ currentTimerSessionId: response.data?.id });

            // Start background timer tracking
            try {
              await backgroundTimerService.startBackgroundTimer({
                sessionId: response.data?.id,
                startTime: now,
                durationSet: get().timerDuration * 60,
                endTime: endTime,
              });
              console.log("Background timer started successfully");
            } catch (bgError) {
              console.error("Failed to start background timer:", bgError);
            }
          }
        } catch (error) {
          console.error("Failed to start timer session on backend:", error);
        }
      },

      pauseTimer: async () => {
        if (get().isTimerRunning && !get().isPaused) {
          set({
            isPaused: true,
            pauseStartTime: new Date(),
          });

          // Stop background timer when paused
          try {
            await backgroundTimerService.stopBackgroundTimer();
            console.log("Background timer stopped due to pause");
          } catch (error) {
            console.error("Failed to stop background timer on pause:", error);
          }
        }
      },

      resumeTimer: async () => {
        if (get().isPaused) {
          const now = new Date();
          const pauseDuration = now - get().pauseStartTime;
          const state = get();

          set((prevState) => ({
            isPaused: false,
            pauseStartTime: null,
            totalPausedTime: prevState.totalPausedTime + pauseDuration,
          }));

          // Restart background timer when resumed
          try {
            const newEndTime = new Date(state.timerEndTime.getTime() + pauseDuration);
            set({ timerEndTime: newEndTime });

            await backgroundTimerService.startBackgroundTimer({
              sessionId: state.currentTimerSessionId,
              startTime: now,
              durationSet: state.remainingTime,
              endTime: newEndTime,
            });
            console.log("Background timer restarted after resume");
          } catch (error) {
            console.error("Failed to restart background timer on resume:", error);
          }
        }
      },

      endTimer: async () => {
        const {
          timerDuration,
          remainingTime,
          currentTimerSessionId,
        } = get();

        const now = new Date();

        const durationConsumed = timerDuration * 60 - remainingTime;

        const response = await deviceTimerService.endTimerSession({
          sessionId: currentTimerSessionId,
          durationConsumed: durationConsumed,
          endTime: now,
        });

        if (!response.success) {
          console.error("Failed to end timer on backend:", response.error);
        }

        // Stop background timer
        try {
          await backgroundTimerService.stopBackgroundTimer();
          console.log("Background timer stopped due to timer end");
        } catch (error) {
          console.error("Failed to stop background timer on end:", error);
        }

        set((state) => ({
          currentTimerSessionId: null, lastSyncTime: now,
          isTimerRunning: false,
          isPaused: false,
          timerStartTime: null,
          timerEndTime: null,
          pauseStartTime: null,
          totalPausedTime: 0,
          timerHistory: [...state.timerHistory],
        }));

      },

      updateRemainingTime: (seconds) => set({ remainingTime: seconds }),

      resetTimer: () =>
        set({
          isTimerRunning: false,
          isPaused: false,
          timerStartTime: null,
          timerEndTime: null,
          pauseStartTime: null,
          totalPausedTime: 0,
          remainingTime: get().timerDuration * 60,
        }),

      getTimerHistory: async () => {
        set((state) => ({ ...state, isLoadingTimerHistory: true }));

        const res = await deviceTimerService.getAllTimerHistory();

        if (res.success) {
          const formattedHistory = res.data.map(item => ({
            id: item?.id,
            startTime: new Date(item.startTime),
            endTime: item.endTime ? new Date(item.endTime) : null,
            duration: item.durationSet,
            durationConsumed: item?.durationConsumed || 0,
            completed: item?.isSessionExpired
          }));

          formattedHistory.sort((a, b) => b.startTime - a.startTime);

          const totalSeconds = formattedHistory.reduce((total, timer) => {
            const usedDuration = timer?.durationConsumed;
            return total + usedDuration;
          }, 0);

          set((state) => ({
            ...state,
            isLoadingTimerHistory: false,
            timerHistory: formattedHistory,
            totalUserTime: totalSeconds,
            lastDeviceLogTime: formattedHistory[0]?.startTime,
          }));
        } else {
          set((state) => ({
            ...state,
            isLoadingTimerHistory: false,
            timerHistoryError: res.error,
          }));
        }
      },

      syncTimerWithBackend: async () => {
        // Only sync if timer is running or paused
        if (!get().isTimerRunning) return;

        const {
          timerStartTime,
          timerDuration,
          remainingTime,
          currentTimerSessionId,
          lastSyncTime,
        } = get();
        const now = new Date();

        // Skip sync if it was just started (within the last 2 seconds)
        // This prevents double API calls when the timer is first started
        if (lastSyncTime && now - lastSyncTime < 2000) {
          console.log("Skipping sync - timer was just started or updated");
          return;
        }

        // Calculate elapsed time in seconds
        const elapsedSeconds = timerDuration * 60 - remainingTime;
        const durationConsumed = elapsedSeconds; // Keep in seconds for the API

        try {
          // If we have a session ID, update the existing session
          if (currentTimerSessionId) {
            await deviceTimerService.updateTimerSession({
              sessionId: currentTimerSessionId,
              durationConsumed: durationConsumed,
              // endTime: new Date(timerStartTime.getTime() + timerDuration * 60 * 1000)
            });

            // Update last sync time
            set({ lastSyncTime: now });
            console.log("Timer data synced with backend - updated session");
          } else {
            // If no session ID, create a new timer
            const response = await deviceTimerService.createTimer({
              durationSet: timerDuration * 60, // Convert minutes to seconds for the API
              startTime: timerStartTime,
            });

            // Save the session ID for future updates
            if (response.success && response.sessionId) {
              set({
                currentTimerSessionId: response.sessionId,
                lastSyncTime: now,
              });
              console.log(
                "Timer data synced with backend - created new session"
              );
            }
          }
        } catch (error) {
          console.error("Failed to sync timer data with backend:", error);
        }
      },

      setDeviceGraphTimeFilter: (filter) => set({ deviceTimeGraphFilter: filter }),

      getTimerGraphRecords: async () => {
        const filter = get().deviceTimeGraphFilter;

        set((state) => ({ ...state, isLoadingDeviceGraphRecords: true }));

        const res = await deviceTimerService.getTimerGraphRecords(filter);

        if (res.success) {
          set((state) => ({
            ...state,
            isLoadingDeviceGraphRecords: false,
            deviceTimeGraphData: res.data.graphData,
            deviceTimeGraphTimePeriod: res.data.timePeriod,
          }));
          return true;
        } else {
          set((state) => ({
            ...state,
            isLoadingDeviceGraphRecords: false,
            deviceTimeGraphError: res.error,
          }));
          return false;
        }
      },

      getTimerHighlights: async () => {
        set((state) => ({ ...state, isLoadingDeviceTimerHighlights: true }));

        const res = await highlightsService.getHighlights("device");

        if (res.success) {
          set((state) => ({
            ...state,
            isLoadingDeviceTimerHighlights: false,
            deviceTimerHighlights: res.data,
          }));
        } else {
          set((state) => ({
            ...state,
            isLoadingDeviceTimerHighlights: false,
            deviceTimerHighlightsError: res.error,
          }));
        }
      },

      setDeviceTimerStoreError: (error) => {
        set((state) => ({ ...state, deviceTimeGraphError: error }));
      },

      // Check and sync with background timer state
      checkBackgroundTimerState: async () => {
        try {
          const backgroundState = await backgroundTimerService.getBackgroundTimerState();

          if (!backgroundState.success || !backgroundState.data) {
            return { success: true, timerEnded: false };
          }

          const currentState = get();
          const now = new Date();
          const endTime = new Date(backgroundState.data.endTime);

          // Check if timer should have ended while app was closed
          if (now >= endTime) {
            console.log('Timer ended while app was closed');

            // End the timer session on backend
            try {
              await deviceTimerService.endTimerSession({
                sessionId: backgroundState.data.sessionId,
                durationConsumed: backgroundState.data.durationSet,
                endTime: endTime,
              });
            } catch (error) {
              console.error('Failed to end timer session after background completion:', error);
            }

            // Clean up background timer state
            await backgroundTimerService.stopBackgroundTimer();

            // Reset timer state
            set({
              isTimerRunning: false,
              isPaused: false,
              timerStartTime: null,
              timerEndTime: null,
              pauseStartTime: null,
              totalPausedTime: 0,
              remainingTime: currentState.timerDuration * 60,
              currentTimerSessionId: null,
            });

            return { success: true, timerEnded: true, endTime: endTime };
          }

          // If timer is still running, update remaining time
          if (currentState.isTimerRunning && !currentState.isPaused) {
            const startTime = new Date(backgroundState.data.startTime);
            const elapsedSeconds = Math.floor((now - startTime) / 1000);
            const newRemainingTime = Math.max(0, backgroundState.data.durationSet - elapsedSeconds);

            set({ remainingTime: newRemainingTime });
            console.log('Updated remaining time from background state:', newRemainingTime);
          }

          return { success: true, timerEnded: false };
        } catch (error) {
          console.error('Failed to check background timer state:', error);
          return { success: false, error: error.message };
        }
      },

      // Initialize background timer service
      initializeBackgroundTimer: async () => {
        try {
          await backgroundTimerService.registerBackgroundTask();
          console.log('Background timer service initialized');
          return { success: true };
        } catch (error) {
          console.error('Failed to initialize background timer service:', error);
          return { success: false, error: error.message };
        }
      },

    }),
    {
      name: STORAGE_KEY,
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

export default useDeviceTimerStore;
