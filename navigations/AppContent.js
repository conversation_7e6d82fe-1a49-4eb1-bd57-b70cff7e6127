import React, { useEffect } from "react";
import { View, StyleSheet, Modal } from "react-native";
import { NavigationContainer } from "@react-navigation/native";
import { AuthContext, useAuth } from "../context/AuthContext";
import RootStackNav from './RootStackNav';
import StartUpStackNav from './StartUpStackNav';
import BottomTabStackNav from './BottomTabStackNav';
import { Colors } from 'constants/theme/colors';
import CustomLoader from 'components/CustomAction/CustomLoader';
import { CustomAlert } from "components/CustomAction";
import useGlobalErrorStore from "store/globalErrorStore";
import GlobalLoader from "./GlobalLoader";
import Toast from "react-native-toast-message";
import userService from 'services/userService';
import authService from 'services/authService';

export const AppContent = () => {
	const { state, isProfileCompleted } = useAuth();
	const { healthDataError, setHealthDataError } = useGlobalErrorStore(state => state);

	useEffect(() => {
		if (healthDataError) {
			Toast.show({
				type: 'error',
				text1: "Health error",
				text2: healthDataError,
				position: 'bottom',
				autoHide: true,
				bottomOffset: 80,
				hideOnPress: true,
			});

			setHealthDataError(null);
		}
	}, [healthDataError]);

	// Show loading screen while checking authentication status
	if (state.isLoading) {
		return (
			<View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
				<CustomLoader />
			</View>
		);
	}

	const StartUp = () => {
		// Check if user is authenticated
		if (!state.userToken || !state.user) {
			return <RootStackNav />; // Show login/register stack
		}
		// Check if profile is completed
		// const isProfileComplete = state.user.isAccountCompleted;
		const isProfileComplete = isProfileCompleted();
		console.log('isProfileComplete ===', isProfileComplete);
		if (!isProfileComplete) {
			return <StartUpStackNav />; // Show onboarding flow
		}

		useEffect(() => {
			(async () => {
				let success = false;

				while (!success) {
					success = (await authService.updateUserTimeZone()).success;
				}
			})();
		}, []);

		return (
			<>
				<BottomTabStackNav />
				{/* <CustomAlert title="Error" message={healthDataError} visible={!!healthDataError} buttons={[{ text: "OK", onPress: () => setHealthDataError(null), style: "allowButton" }]} onClose={() => setHealthDataError(null)} /> */}
				<GlobalLoader />
			</>
		);
	};

	return (
		<>
			<StartUp />
		</>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: 'center',
		backgroundColor: Colors.primaryGreen,
	},
});