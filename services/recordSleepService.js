import getDayName from "utils/dateandtimeformatters/getDayName";
import apiClient from "./axiosInstance";
import getMonthName from "utils/dateandtimeformatters/getMonthName";
import { getTimePeriod } from "utils/userrecords/getTimePeriod";

export const recordSleepService = {
  logSleepData: async ({ date, duration }) => {
    // console.log(new Date().toISOString().split('T')[0]);
    try {
      const res = await apiClient.post("/sleep_records", {
        numOfHours: duration,
        date: date,
      });

      return {
        success: true,
        message: res?.msg || "Sleep data added successfully!!",
      };
    } catch (error) {
      console.log(error);

      return {
        success: false,
        error: error?.message || "Error recoding sleep.",
      };
    }
  },
  getLastLoggedData: async () => {
    try {
      const res = await apiClient.get("/sleep_records");

      return {
        success: true,
        data: res.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error fetching recent sleep logged data.",
      };
    }
  },
  getSleepRecord: async (date) => {
    try {
      const res = await apiClient.get(`/sleep_records?date=${date}`);

      return {
        success: true,
        data: res.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error fetching sleep records.",
      };
    }
  },
  getSleepGraphRecords: async (filter) => {
    try {
      const res = await apiClient.get(`/sleep_analytics?filter=${filter}`);

      let sleepGraphRecords = [];
      let timePeriod = null;

      if (filter === "weekly") {
        sleepGraphRecords = res.data.map((item) => {
          return {
            value: item?.averageSleep || 0,
            label: getDayName(item.period).substring(0, 1),
          };
        });

        timePeriod = getTimePeriod(
          res.data[0].period,
          res.data[res.data.length - 1].period
        );
      } else if (filter === "monthly") {
        sleepGraphRecords = res.data.map((item) => {
          const weekNumber = item.period.match(/week_(\d+)/);
          return {
            value: item?.averageSleep || 0,
            label: `W${weekNumber[1]}`,
          };
        });

        timePeriod = getTimePeriod(
          res.data[0].startDate,
          res.data[res.data.length - 1].endDate
        );
      } else if (filter === "half_yearly") {
        sleepGraphRecords = res.data.map((item) => {
          return {
            value: item?.averageSleep || 0,
            label: item.period.substring(0, 1),
          };
        });
        timePeriod =
          res.data[0].year !== res.data[res.data.length - 1].year
            ? `${res.data[0].period.substring(0, 3)} ${res.data[0].year
            } - ${res.data[res.data.length - 1].period.substring(0, 3)} ${res.data[res.data.length - 1].year
            }`
            : `${res.data[0].period.substring(0, 3)} - ${res.data[
              res.data.length - 1
            ].period.substring(0, 3)} ${res.data[0].year}`;
      } else if (filter === "yearly") {
        sleepGraphRecords = res.data.map((item) => {
          return {
            value: item?.averageSleep || 0,
            label: item.period.substring(0, 1),
          };
        });
        timePeriod =
          res.data[0].year !== res.data[res.data.length - 1].year
            ? `${res.data[0].period.substring(0, 3)} ${res.data[0].year
            } - ${res.data[res.data.length - 1].period.substring(0, 3)} ${res.data[res.data.length - 1].year
            }`
            : `${res.data[0].period.substring(0, 3)} - ${res.data[
              res.data.length - 1
            ].period.substring(0, 3)} ${res.data[0].year}`;
      }

      return {
        success: true,
        data: {
          graphData: sleepGraphRecords,
          timePeriod: timePeriod,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error fetching recent sleep logged data.",
      };
    }
  },
  getSleepHighlights: async () => {
    try {
      const res = await apiClient.get("/user-highlights/sleep");

      return {
        success: true,
        data: res?.data,
      };
    } catch (error) {
      return {
        success: false,
        error: error?.message || "Error fetching recommended video",
      };
    }
  },
};
