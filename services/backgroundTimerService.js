import * as BackgroundFetch from 'expo-background-fetch';
import * as TaskManager from 'expo-task-manager';
import * as Notifications from 'expo-notifications';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { deviceTimerService } from './deviceTimerService';

const BACKGROUND_TIMER_TASK = 'background-timer-task';
const TIMER_NOTIFICATION_ID = 'device-timer-notification';
const TIMER_STATE_KEY = 'background-timer-state';

// Define the background task
TaskManager.defineTask(BACKGROUND_TIMER_TASK, async () => {
  try {
    console.log('Background timer task executed');

    // Get the current timer state from AsyncStorage
    const timerStateJson = await AsyncStorage.getItem(TIMER_STATE_KEY);
    if (!timerStateJson) {
      console.log('No background timer state found');
      return BackgroundFetch.BackgroundFetchResult.NoData;
    }

    const timerState = JSON.parse(timerStateJson);
    const now = new Date();

    // Check if timer should have ended
    if (now >= new Date(timerState.endTime)) {
      console.log('Timer ended in background, cleaning up');

      // Show immediate notification that timer ended
      try {
        const durationMinutes = Math.round(timerState.durationSet / 60);
        await Notifications.scheduleNotificationAsync({
          content: {
            title: 'Device Timer Ended',
            body: `Your ${durationMinutes} minute timer has ended!`,
            sound: 'sound4.wav',
            data: {
              type: 'timer-ended',
              url: 'appetec://device'
            },
          },
          trigger: null, // Show immediately
        });
        console.log('Timer end notification shown from background task');
      } catch (error) {
        console.error('Failed to show timer end notification in background:', error);
      }

      // End the timer session on the backend
      try {
        const durationConsumed = timerState.durationSet;
        await deviceTimerService.endTimerSession({
          sessionId: timerState.sessionId,
          durationConsumed: durationConsumed,
          endTime: now,
        });
      } catch (error) {
        console.error('Failed to end timer session in background:', error);
      }

      // Clear the background timer state
      await AsyncStorage.removeItem(TIMER_STATE_KEY);

      // Cancel any scheduled notifications (the original scheduled one)
      await Notifications.cancelScheduledNotificationAsync(TIMER_NOTIFICATION_ID);

      return BackgroundFetch.BackgroundFetchResult.NewData;
    }

    // Update the timer session on the backend
    try {
      const elapsedSeconds = Math.floor((now - new Date(timerState.startTime)) / 1000);
      const durationConsumed = Math.min(elapsedSeconds, timerState.durationSet);

      await deviceTimerService.updateTimerSession({
        sessionId: timerState.sessionId,
        durationConsumed: durationConsumed,
      });

      console.log('Timer session updated in background');
    } catch (error) {
      console.error('Failed to update timer session in background:', error);
    }

    return BackgroundFetch.BackgroundFetchResult.NewData;
  } catch (error) {
    console.error('Background timer task error:', error);
    return BackgroundFetch.BackgroundFetchResult.Failed;
  }
});

export const backgroundTimerService = {
  // Register the background task
  registerBackgroundTask: async () => {
    try {
      const status = await BackgroundFetch.getStatusAsync();
      console.log('Background fetch status:', status);

      if (status === BackgroundFetch.BackgroundFetchStatus.Available) {
        // Unregister any existing task first
        try {
          await BackgroundFetch.unregisterTaskAsync(BACKGROUND_TIMER_TASK);
        } catch (e) {
          // Ignore error if task wasn't registered
        }

        await BackgroundFetch.registerTaskAsync(BACKGROUND_TIMER_TASK, {
          minimumInterval: 15, // 15 seconds minimum interval for more frequent checks
          stopOnTerminate: false,
          startOnBoot: true,
        });
        console.log('Background timer task registered successfully');
        return { success: true };
      } else {
        console.log('Background fetch not available, status:', status);
        return { success: false, error: `Background fetch not available. Status: ${status}` };
      }
    } catch (error) {
      console.error('Failed to register background timer task:', error);
      return { success: false, error: error.message };
    }
  },

  // Unregister the background task
  unregisterBackgroundTask: async () => {
    try {
      await BackgroundFetch.unregisterTaskAsync(BACKGROUND_TIMER_TASK);
      console.log('Background timer task unregistered');
      return { success: true };
    } catch (error) {
      console.error('Failed to unregister background timer task:', error);
      return { success: false, error: error.message };
    }
  },

  // Start background timer tracking
  startBackgroundTimer: async ({ sessionId, startTime, durationSet, endTime }) => {
    try {
      // Store timer state in AsyncStorage
      const timerState = {
        sessionId,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        durationSet, // in seconds
        isActive: true,
      };

      await AsyncStorage.setItem(TIMER_STATE_KEY, JSON.stringify(timerState));

      // Schedule a notification for when the timer ends (as fallback)
      await backgroundTimerService.scheduleTimerEndNotification(endTime, durationSet);

      console.log('Background timer started with end time:', endTime);
      console.log('Timer duration in seconds:', durationSet);
      return { success: true };
    } catch (error) {
      console.error('Failed to start background timer:', error);
      return { success: false, error: error.message };
    }
  },

  // Stop background timer tracking
  stopBackgroundTimer: async () => {
    try {
      // Remove timer state from AsyncStorage
      await AsyncStorage.removeItem(TIMER_STATE_KEY);

      // Cancel any scheduled notifications
      await Notifications.cancelScheduledNotificationAsync(TIMER_NOTIFICATION_ID);

      console.log('Background timer stopped');
      return { success: true };
    } catch (error) {
      console.error('Failed to stop background timer:', error);
      return { success: false, error: error.message };
    }
  },

  // Get current background timer state
  getBackgroundTimerState: async () => {
    try {
      const timerStateJson = await AsyncStorage.getItem(TIMER_STATE_KEY);
      if (!timerStateJson) {
        return { success: true, data: null };
      }

      const timerState = JSON.parse(timerStateJson);
      return { success: true, data: timerState };
    } catch (error) {
      console.error('Failed to get background timer state:', error);
      return { success: false, error: error.message };
    }
  },

  // Schedule notification for timer end
  scheduleTimerEndNotification: async (endTime, durationSeconds) => {
    try {
      // Cancel any existing timer notification
      await Notifications.cancelScheduledNotificationAsync(TIMER_NOTIFICATION_ID);

      // Convert seconds to minutes for display
      const durationMinutes = Math.round(durationSeconds / 60);

      // Schedule new notification
      await Notifications.scheduleNotificationAsync({
        identifier: TIMER_NOTIFICATION_ID,
        content: {
          title: 'Device Timer Ended',
          body: `Your ${durationMinutes} minute timer has finished!`,
          sound: 'sound4.wav',
          data: {
            type: 'timer-ended',
            url: 'appetec://device'
          },
        },
        trigger: {
          date: endTime,
        },
      });

      console.log('Timer end notification scheduled for:', endTime);
      return { success: true };
    } catch (error) {
      console.error('Failed to schedule timer end notification:', error);
      return { success: false, error: error.message };
    }
  },

  // Show immediate notification when timer starts
  showTimerStartNotification: async (durationSeconds) => {
    try {
      // Convert seconds to minutes for display
      const durationMinutes = Math.round(durationSeconds / 60);

      // Show immediate notification
      await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Device Timer Started',
          body: `Your ${durationMinutes} minute timer has started and will continue running in background`,
          sound: 'default',
          data: {
            type: 'timer-started',
            url: 'appetec://device'
          },
        },
        trigger: null, // Show immediately
      });

      console.log('Timer start notification shown immediately');
      return { success: true };
    } catch (error) {
      console.error('Failed to show timer start notification:', error);
      return { success: false, error: error.message };
    }
  },

  // Show immediate notification when timer ends
  showTimerEndNotification: async (durationSeconds) => {
    try {
      // Convert seconds to minutes for display
      const durationMinutes = Math.round(durationSeconds / 60);

      // Show immediate notification
      await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Device Timer Ended',
          body: `Your ${durationMinutes} minute timer has ended!`,
          sound: 'sound4.wav',
          data: {
            type: 'timer-ended',
            url: 'appetec://device'
          },
        },
        trigger: null, // Show immediately
      });

      console.log('Timer end notification shown immediately');
      return { success: true };
    } catch (error) {
      console.error('Failed to show timer end notification:', error);
      return { success: false, error: error.message };
    }
  },

  // Check if background timer is supported
  isBackgroundTimerSupported: async () => {
    try {
      const status = await BackgroundFetch.getStatusAsync();
      return status === BackgroundFetch.BackgroundFetchStatus.Available;
    } catch (error) {
      console.error('Failed to check background timer support:', error);
      return false;
    }
  },

  // Sync background timer with current state
  syncBackgroundTimer: async (timerState) => {
    try {
      const backgroundState = await backgroundTimerService.getBackgroundTimerState();

      if (!backgroundState.success) {
        return { success: false, error: backgroundState.error };
      }

      // If no background timer is running but app timer is running, start background timer
      if (!backgroundState.data && timerState.isTimerRunning && !timerState.isPaused) {
        return await backgroundTimerService.startBackgroundTimer({
          sessionId: timerState.currentTimerSessionId,
          startTime: new Date(timerState.timerStartTime),
          durationSet: timerState.timerDuration * 60,
          endTime: new Date(timerState.timerEndTime),
        });
      }

      // If background timer is running but app timer is not, stop background timer
      if (backgroundState.data && (!timerState.isTimerRunning || timerState.isPaused)) {
        return await backgroundTimerService.stopBackgroundTimer();
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to sync background timer:', error);
      return { success: false, error: error.message };
    }
  },
};
