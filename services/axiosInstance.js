import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import { Alert, Platform } from 'react-native';
import { navigate } from '../navigations/RootNavigation';

const apiUrl = process.env.EXPO_PUBLIC_API_URL;

// Create axios instance
const apiClient = axios.create({
    baseURL: apiUrl,
    timeout: 10000, // 10 seconds timeout
    headers: {
        'Content-Type': 'application/json',
    },
});

// Flag to prevent multiple refresh token requests
let isRefreshing = false;
// Queue of requests to retry after token refresh
let refreshSubscribers = [];

// Function to add callbacks to the queue
const addSubscriber = (callback) => {
    refreshSubscribers.push(callback);
};

// Function to execute all callbacks in the queue with the new token
const onRefreshed = (token) => {
    refreshSubscribers.forEach(callback => callback(token));
    refreshSubscribers = [];
};

// Function to refresh the token
const refreshAccessToken = async () => {
    try {
        // Check if we have a refresh token
        const refreshToken = await SecureStore.getItemAsync('refreshToken');
        if (!refreshToken) {
            console.log('No refresh token available, redirecting to login');
            // Clear tokens and user data
            await SecureStore.deleteItemAsync('accessToken');
            await SecureStore.deleteItemAsync('userData');

            // Redirect to login screen
            if (Platform.OS !== 'web') {
                setTimeout(() => {
                    navigate('Login');
                }, 500);
            }

            throw new Error('No refresh token available');
        }

        // Make a request to refresh the token
        const response = await axios.post(`${apiUrl}/refresh-token`, { refreshToken });
        if (!response.data || !response.data.accessToken) {
            throw new Error('Failed to refresh token');
        }

        // Store the new token
        const newToken = response.data.accessToken;
        await SecureStore.setItemAsync('accessToken', newToken);

        console.log('Token refreshed successfully');
        return newToken;
    } catch (error) {
        console.error('Token refresh failed:', error);
        // Clear tokens and user data
        await SecureStore.deleteItemAsync('accessToken');
        await SecureStore.deleteItemAsync('refreshToken');
        await SecureStore.deleteItemAsync('userData');

        // Redirect to login screen
        if (Platform.OS !== 'web') {
            setTimeout(() => {
                navigate('Login');
            }, 500);
        }

        throw error;
    }
};

// Request Interceptor
apiClient.interceptors.request.use(
    async (config) => {
        try {
            const token = await SecureStore.getItemAsync('accessToken');
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
            }
        } catch (error) {
            console.error('Error setting auth header:', error);
        }
        return config;
    },
    (error) => {
        console.error('Request Error:', error);
        return Promise.reject(error);
    }
);

// Response Interceptor
apiClient.interceptors.response.use(
    (response) => {
        return response.data; // Standardizing response to return only `data`
    },
    async (error) => {
        const originalRequest = error.config;

        // Skip token refresh for authentication-related endpoints
        const authEndpoints = ['/login', '/logout', '/refresh-token', '/register'];
        const isAuthEndpoint = authEndpoints.some(endpoint => originalRequest.url.includes(endpoint));

        // Handle 401 Unauthorized errors, but skip for auth endpoints
        if (error.response && error.response.status === 401 && !originalRequest._retry && !isAuthEndpoint) {
            // Mark this request as retried to prevent infinite loops
            originalRequest._retry = true;

            // If we're not already refreshing the token
            if (!isRefreshing) {
                isRefreshing = true;

                try {
                    // Attempt to refresh the token
                    const newToken = await refreshAccessToken();

                    // Update the Authorization header
                    originalRequest.headers.Authorization = `Bearer ${newToken}`;

                    // Notify all subscribers that the token has been refreshed
                    onRefreshed(newToken);

                    // Retry the original request with the new token
                    return apiClient(originalRequest);
                } catch (refreshError) {
                    // If refresh fails, reject all subscribers
                    refreshSubscribers.forEach(callback => callback(null));
                    refreshSubscribers = [];

                    // Show an alert to the user
                    // Alert.alert(
                    //     "Session Expired",
                    //     "Your session has expired. Please log in again.",
                    //     [{ text: "OK" }]
                    // );

                    return Promise.reject(refreshError);
                } finally {
                    isRefreshing = false;
                }
            } else {
                // If we're already refreshing, add this request to the queue
                return new Promise(resolve => {
                    addSubscriber(token => {
                        if (token) {
                            originalRequest.headers.Authorization = `Bearer ${token}`;
                            resolve(apiClient(originalRequest));
                        } else {
                            // If token refresh failed, reject the promise
                            resolve(Promise.reject(error));
                        }
                    });
                });
            }
        }

        // For other errors, just reject with the response data
        if (error.response) {
            return Promise.reject(error.response.data);
        }

        console.error('Network Error:', error);
        return Promise.reject({ message: 'Network Error. Please try again.' });
    }
);

export default apiClient;