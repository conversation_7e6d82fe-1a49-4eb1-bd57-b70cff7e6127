import apiClient from "./axiosInstance";

export const activityService = {
    // Create a new manual activity entry
    createActivity: async ({ activityType, durationInMinutes }) => {
        try {
            const res = await apiClient.post("/user-health/activity-manual", {
                activityType,
                durationInMinutes,
            });

            return {
                success: true,
                message: res?.msg || "Activity created successfully",
                data: res || null,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error creating activity"
            };
        }
    },

    // Get all activities
    getAllActivities: async () => {
        try {
            const res = await apiClient.get("/user-health/activity-manual");
            return {
                success: true,
                data: res.data,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching activities"
            };
        }
    },

    // Get activity history for a specific date
    getActivityHistory: async (date) => {
        try {
            const res = await apiClient.get(`/user-health/activity-manual?date=${date}`);

            return {
                success: true,
                data: res || [],
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching activity history"
            };
        }
    },
    // Get single activity by ID
    getActivityById: async (id) => {
        try {
            const res = await apiClient.get(`/user-health/single-activity-manual/${id}`);
            return {
                success: true,
                data: res.data,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching activity"
            };
        }
    },

    // Update activity (only duration can be updated)
    updateActivity: async ({ id, durationInMinutes }) => {
        try {
            const res = await apiClient.put(`/user-health/edit-activity-manual/${id}`, {
                durationInMinutes,
            });

            return {
                success: true,
                message: res?.msg || "Activity updated successfully",
                data: res || null,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error updating activity"
            };
        }
    },

    // Delete activity
    deleteActivity: async (id) => {
        try {
            const res = await apiClient.put(`/user-health/delete-activity-manual/${id}`);

            return {
                success: true,
                message: res?.msg || "Activity deleted successfully",
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error deleting activity"
            };
        }
    },

    getTodaysLedger: async () => {
        try {
            const res = await apiClient.get("/user-health/health-ledger");
            return {
                success: true,
                data: res.data,
            };
        } catch (error) {
            return {
                success: false,
                error: error?.message || "Error fetching ledger"
            };
        }
    },

    getRecentActivity: async () => {
        try {
            const res = await apiClient.get("/user-health/last-log-activity");
            return {
                success: true,
                data: res.data,
            };
        } catch (error) {
            console.log(error);
            return {
                success: false,
                error: error?.message || "Error fetching recent activity"
            };
        }
    },
};